import React, { useState } from 'react'
import './Header.css'

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <>
      {/* Top Header Bar */}
      <div className="top-header">
        <div className="container">
          <div className="top-header-content">
            <div className="contact-info">
              <span>(812) 727-7000</span>
              <span>Professionally Managed by Granite Student Living</span>
            </div>
            <div className="top-header-actions">
              <a href="#contact" className="btn btn-small">Schedule A Tour</a>
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <header className="main-header">
        <div className="container">
          <div className="header-content">
            {/* Logo */}
            <div className="logo">
              <a href="#home">
                <img src="/logo.svg" alt="Crimson Crossing Apartments" />
              </a>
            </div>

            {/* Desktop Navigation */}
            <nav className="desktop-nav">
              <ul>
                <li><a href="#amenities">Amenities</a></li>
                <li><a href="#community">Community</a></li>
                <li className="dropdown">
                  <a href="#floorplans">Floorplans</a>
                  <ul className="dropdown-menu">
                    <li><a href="#banner-remodeled">The Banner (remodeled) – 3 Bed/3.5 Bath with Den</a></li>
                    <li><a href="#banner">The Banner – 3 Bed/3.5 Bath with Den</a></li>
                    <li><a href="#crimson">The Crimson – 3 Bed/3.5 Bath</a></li>
                    <li><a href="#knight">The Knight – 3 Bed/2.5 Bath</a></li>
                    <li><a href="#hoosier">The Hoosier – 3 Bed/3.5 Bath with Den</a></li>
                  </ul>
                </li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
              </ul>
            </nav>

            {/* Mobile Menu Toggle */}
            <button 
              className={`mobile-toggle ${isMenuOpen ? 'active' : ''}`}
              onClick={toggleMenu}
              aria-label="Toggle menu"
            >
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div className={`mobile-nav ${isMenuOpen ? 'active' : ''}`}>
          <div className="mobile-nav-content">
            <ul>
              <li><a href="#amenities" onClick={toggleMenu}>Amenities</a></li>
              <li><a href="#community" onClick={toggleMenu}>Community</a></li>
              <li><a href="#floorplans" onClick={toggleMenu}>Floorplans</a></li>
              <li><a href="#about" onClick={toggleMenu}>About</a></li>
              <li><a href="#contact" onClick={toggleMenu}>Contact</a></li>
            </ul>
          </div>
        </div>
      </header>
    </>
  )
}

export default Header
