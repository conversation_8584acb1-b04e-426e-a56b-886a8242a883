.about {
  background: var(--global-palette9);
  width: 100%;
  overflow-x: hidden;
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.about-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.about-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.7;
  color: var(--global-palette5);
}

.about-image {
  position: relative;
}

.about-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0px 15px 35px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .about-content {
    gap: 3rem;
  }
  
  .about-text h2 {
    font-size: 2rem;
  }
  
  .about-image img {
    height: 350px;
  }
}

@media (max-width: 767px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .about-text h2 {
    font-size: 1.8rem;
  }
  
  .about-text p {
    font-size: 1rem;
  }
  
  .about-image img {
    height: 300px;
  }
}
