/* Top Header Bar */
.top-header {
  background: var(--global-palette1);
  color: var(--global-palette7);
  font-size: 13px;
  padding: 0.6em 0;
}

.top-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.contact-info {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.contact-info span {
  color: var(--global-palette7);
}

.top-header-actions .btn {
  font-size: 12px;
  padding: 0.3em 0.8em;
}

/* Main Header */
.main-header {
  background: var(--global-palette9);
  box-shadow: 0px 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80px;
}

/* Logo */
.logo img {
  max-width: 200px;
  height: auto;
}

/* Desktop Navigation */
.desktop-nav {
  display: flex;
}

.desktop-nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2em;
}

.desktop-nav li {
  position: relative;
}

.desktop-nav a {
  color: var(--global-palette4);
  text-decoration: none;
  font-style: normal;
  letter-spacing: 1px;
  text-transform: uppercase;
  padding: 1em 0;
  transition: color 0.3s ease;
}

.desktop-nav a:hover {
  color: var(--global-palette2);
}

/* Dropdown Menu */
.dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: var(--global-palette3);
  box-shadow: 0px 2px 13px 0px rgba(0,0,0,0.1);
  min-width: 315px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.dropdown-menu li {
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.dropdown-menu li:last-child {
  border-bottom: none;
}

.dropdown-menu a {
  display: block;
  padding: 1em;
  color: var(--global-palette8);
  font-size: 12px;
  text-transform: none;
  letter-spacing: normal;
  white-space: nowrap;
}

.dropdown-menu a:hover {
  color: var(--global-palette9);
  background: var(--global-palette4);
}

/* Mobile Toggle */
.mobile-toggle {
  display: none;
  flex-direction: column;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.4em 0.6em;
  gap: 4px;
}

.mobile-toggle span {
  width: 25px;
  height: 3px;
  background: var(--global-palette3);
  transition: all 0.3s ease;
}

.mobile-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation */
.mobile-nav {
  display: none;
  background: var(--global-palette6);
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-20px);
  transition: all 0.3s ease;
}

.mobile-nav.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.mobile-nav-content {
  padding: 2rem 0;
}

.mobile-nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav li {
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mobile-nav a {
  display: block;
  padding: 1em 1.5rem;
  color: var(--global-palette9);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.mobile-nav a:hover {
  color: #efa9aa;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .desktop-nav {
    display: none;
  }
  
  .mobile-toggle {
    display: flex;
  }
  
  .mobile-nav {
    display: block;
  }
  
  .logo img {
    max-width: 180px;
  }
}

@media (max-width: 767px) {
  .top-header-content {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .contact-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .header-content {
    min-height: 70px;
  }
}
