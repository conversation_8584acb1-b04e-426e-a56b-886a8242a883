/* Main Footer */
.footer-main {
  background: var(--global-palette4);
  color: var(--global-palette9);
  padding: 60px 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 3rem;
}

.footer-section h4 {
  color: var(--global-palette7);
  margin-bottom: 1rem;
  font-size: 22px;
}

.footer-section h5 {
  color: var(--global-palette9);
  margin-bottom: 0.5rem;
  font-size: 18px;
}

.footer-logo img {
  max-width: 100px;
  margin-bottom: 1rem;
}

.property-info p,
.office-info p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.get-directions {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--global-palette1);
  text-decoration: none;
  font-size: 16px;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.3s ease;
}

.get-directions:hover {
  color: var(--global-palette2);
}

.office-info a {
  color: #efa9aa;
  text-decoration: none;
}

.office-info a:hover {
  color: var(--global-palette9);
}

.footer-menu {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-top: 12px;
  margin-bottom: 20px;
}

.footer-menu li a {
  color: var(--global-palette9);
  text-decoration: none;
  padding: 0;
  transition: color 0.3s ease;
}

.footer-menu li a:hover {
  color: #efa9aa;
  text-decoration: underline;
}

/* Bottom Footer */
.footer-bottom {
  background: var(--global-palette3);
  color: var(--global-palette7);
  padding: 20px 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 2rem;
}

.footer-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.footer-social a {
  color: var(--global-palette7);
  text-decoration: none;
  font-size: 1.2rem;
  transition: color 0.3s ease;
}

.footer-social a:hover {
  color: #efa9aa;
}

.footer-right {
  text-align: right;
  max-width: 400px;
}

.disclaimer {
  font-size: 12px;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.website-credit {
  font-size: 16px;
  color: var(--global-palette7);
}

/* Special Offer Banner */
.special-offer {
  background: var(--global-palette1);
  color: var(--global-palette9);
  padding: 20px 0;
  text-align: center;
}

.offer-content h3 {
  color: var(--global-palette9);
  margin-bottom: 0.5rem;
  font-size: 18px;
}

.offer-content p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-main {
    padding: 40px 0;
  }
  
  .footer-content {
    gap: 2rem;
  }
}

@media (max-width: 767px) {
  .footer-main {
    padding: 30px 0;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .footer-right {
    text-align: center;
    max-width: none;
  }
  
  .footer-left {
    justify-content: center;
  }
  
  .offer-content h3 {
    font-size: 16px;
  }
  
  .offer-content p {
    font-size: 13px;
  }
}
