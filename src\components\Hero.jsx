import React from 'react'
import './Hero.css'

const Hero = () => {
  return (
    <section className="hero" id="home">
      <div className="hero-background">
        <div className="hero-video-container">
          {/* Placeholder for video - in a real implementation, you'd add a video element */}
          <div className="hero-image">
            <img 
              src="https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
              alt="Crimson Crossing Apartments" 
            />
          </div>
        </div>
        <div className="hero-overlay"></div>
      </div>
      
      <div className="hero-content">
        <div className="container">
          <div className="hero-text">
            <h1>Crimson Crossing Apartments</h1>
            <h2>In Bloomington, Indiana</h2>
            <p className="hero-subtitle">
              3-4 bedroom apartments near Indiana University, IU Stadium, & downtown Bloomington
            </p>
            <div className="hero-actions">
              <a href="#contact" className="btn btn-primary">Contact Us</a>
              <a href="#floorplans" className="btn btn-outline">Schedule a Tour</a>
            </div>
          </div>
        </div>
      </div>
      
      <div className="hero-play-button">
        <button className="play-btn" aria-label="Play video">
          <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
            <circle cx="30" cy="30" r="30" fill="rgba(255,255,255,0.9)"/>
            <path d="M25 20L40 30L25 40V20Z" fill="var(--global-palette1)"/>
          </svg>
        </button>
      </div>
    </section>
  )
}

export default Hero
