.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.hero-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.hero-image {
  width: 100%;
  height: 100%;
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(153, 29, 32, 0.7) 0%,
    rgba(188, 36, 38, 0.6) 50%,
    rgba(65, 64, 66, 0.8) 100%
  );
  z-index: 2;
}

.hero-content {
  position: relative;
  z-index: 3;
  text-align: center;
  color: var(--global-palette9);
  width: 100%;
}

.hero-text h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--global-palette9);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-text h2 {
  font-size: 2.5rem;
  font-weight: 400;
  margin-bottom: 1rem;
  color: var(--global-palette9);
  text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.hero-actions .btn {
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  min-width: 160px;
}

.btn-primary {
  background-color: var(--global-palette1);
  color: var(--global-palette9);
}

.btn-primary:hover {
  background-color: var(--global-palette2);
}

.hero-actions .btn-outline {
  background-color: transparent;
  color: var(--global-palette9);
  border: 2px solid var(--global-palette9);
}

.hero-actions .btn-outline:hover {
  background-color: var(--global-palette9);
  color: var(--global-palette1);
}

.hero-play-button {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 4;
}

.play-btn {
  background: none;
  border: none;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.play-btn:hover {
  transform: scale(1.1);
}

.play-btn svg {
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-text h1 {
    font-size: 3rem;
  }
  
  .hero-text h2 {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 767px) {
  .hero {
    min-height: 80vh;
  }
  
  .hero-text h1 {
    font-size: 2.5rem;
  }
  
  .hero-text h2 {
    font-size: 1.8rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .hero-actions .btn {
    width: 100%;
    max-width: 280px;
  }
  
  .hero-play-button {
    bottom: 1rem;
  }
  
  .play-btn svg {
    width: 50px;
    height: 50px;
  }
}
