import React from 'react'
import './Footer.css'

const Footer = () => {
  return (
    <footer className="footer">
      {/* Main Footer */}
      <div className="footer-main">
        <div className="container">
          <div className="footer-content">
            {/* Property Info */}
            <div className="footer-section">
              <div className="footer-logo">
                <img src="/logo.svg" alt="Crimson Crossing Apartments" />
              </div>
              <div className="property-info">
                <h4>Property Address</h4>
                <p>1426 N Kinser Pike<br />Bloomington, IN 47404</p>
                <a href="#" className="get-directions">
                  <span>📍</span> Get Directions
                </a>
              </div>
            </div>

            {/* Management Info */}
            <div className="footer-section">
              <h4>Managed By Granite</h4>
              <div className="office-info">
                <h5>Office Address</h5>
                <p>401 E. 4th Street<br />Bloomington, IN 47408</p>
                <p><strong>(812) 727-7000</strong></p>
                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
              </div>
            </div>

            {/* Tenant Resources */}
            <div className="footer-section">
              <h4>🏠 Tenant Resources</h4>
              <ul className="footer-menu">
                <li><a href="#payment">Payment Options</a></li>
                <li><a href="#services">Resident Services</a></li>
                <li><a href="#maintenance">Maintenance</a></li>
                <li><a href="#move-in">Move In Resources</a></li>
                <li><a href="#faq">FAQ</a></li>
              </ul>
            </div>

            {/* Quick Links */}
            <div className="footer-section">
              <h4>Quick Links</h4>
              <ul className="footer-menu">
                <li><a href="#amenities">Amenities</a></li>
                <li><a href="#community">Community</a></li>
                <li><a href="#floorplans">Floor Plans</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="footer-bottom">
        <div className="container">
          <div className="footer-bottom-content">
            <div className="footer-left">
              <p>&copy; 2025 Crimson Crossing Apartments</p>
              <div className="footer-social">
                <a href="#" aria-label="YouTube">
                  <span>📺</span>
                </a>
              </div>
            </div>
            <div className="footer-right">
              <p className="disclaimer">
                Property information is believed to be correct but is subject to change 
                and could contain typographical errors. Please contact us directly to get 
                the most accurate information.
              </p>
              <p className="website-credit">
                Website By Toohill Consulting
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Special Offer Banner */}
      <div className="special-offer">
        <div className="container">
          <div className="offer-content">
            <h3>CHECK OUT OUR CURRENT SPECIAL!</h3>
            <p>
              Sign a lease by July 11th and get a month of rent free! Contact us today at 
              (812) 727-7000 or online to learn more about this special offer.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
