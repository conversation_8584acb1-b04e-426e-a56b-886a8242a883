.reviews {
  background: var(--global-palette8);
  width: 100%;
  overflow-x: hidden;
}

.reviews-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.review-card {
  background: var(--global-palette9);
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0px 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
}

.review-card:hover {
  transform: translateY(-5px);
  box-shadow: 0px 15px 35px rgba(0,0,0,0.15);
}

.review-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 4rem;
  color: var(--global-palette1);
  font-family: serif;
  line-height: 1;
}

.review-header {
  margin-bottom: 1rem;
}

.review-rating {
  margin-bottom: 0.5rem;
}

.star {
  color: #ddd;
  font-size: 1.2rem;
  margin-right: 2px;
}

.star.filled {
  color: #ffd700;
}

.review-card h3 {
  font-size: 1.3rem;
  color: var(--global-palette3);
  margin-bottom: 1rem;
}

.review-text {
  font-style: italic;
  line-height: 1.6;
  color: var(--global-palette5);
  margin-bottom: 1.5rem;
  font-size: 1rem;
}

.review-author {
  color: var(--global-palette1);
  font-weight: 600;
  text-align: right;
}

.reviews-cta {
  text-align: center;
  background: var(--global-palette9);
  padding: 3rem 2rem;
  border-radius: 12px;
  box-shadow: 0px 5px 15px rgba(0,0,0,0.08);
}

.reviews-cta p {
  font-size: 1.2rem;
  color: var(--global-palette4);
  margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .reviews-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 767px) {
  .reviews-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .review-card {
    padding: 1.5rem;
  }
  
  .review-card::before {
    font-size: 3rem;
    top: -5px;
    left: 15px;
  }
  
  .reviews-cta {
    padding: 2rem 1rem;
  }
  
  .reviews-cta p {
    font-size: 1.1rem;
  }
}
