import React from 'react'
import './Reviews.css'

const Reviews = () => {
  const reviews = [
    {
      name: '<PERSON><PERSON>',
      title: 'Felt Like My Second Home',
      review: 'I definitely recommend to all students looking for quality apartments to stay in. I lived there this past school year and it felt like my second home. The staff was always super friendly and willing to help answer any of my question to the best of their ability.',
      rating: 5
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      title: 'Best place in Bloomington!',
      review: 'I moved in my sophomore year at Indiana University and didn\'t move out till I graduated! The floor plans were big and always had room for guests. Management was awesome and always got back to me ASAP with questions or concerns...',
      rating: 5
    },
    {
      name: '<PERSON><PERSON>',
      title: 'Had a Wonderful Experience',
      review: 'I just moved out and had a wonderful experience there. Maintenance was very responsive and took care of all my work orders in a timely fashion.',
      rating: 5
    },
    {
      name: '<PERSON>',
      title: 'Above and Beyond',
      review: '...goes above and beyond to exceed expectations for residents! Top-notch property management team! Not to mention the clean, high-quality and spacious townhomes they have to offer...',
      rating: 5
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      title: 'Super Friendly',
      review: 'I just moved in last week and already like it here. The people here are super friendly and always willing to help. Not to mention great apartments.',
      rating: 5
    }
  ]

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span key={index} className={`star ${index < rating ? 'filled' : ''}`}>
        ★
      </span>
    ))
  }

  return (
    <section className="reviews section-large" id="reviews">
      <div className="container">
        <div className="section-header">
          <h2>Reviews From Residents</h2>
          <p>
            See what our residents have to say about their experience living at 
            Crimson Crossing Apartments.
          </p>
        </div>

        <div className="reviews-grid">
          {reviews.map((review, index) => (
            <div key={index} className="review-card">
              <div className="review-header">
                <div className="review-rating">
                  {renderStars(review.rating)}
                </div>
                <h3>{review.title}</h3>
              </div>
              <p className="review-text">"{review.review}"</p>
              <div className="review-author">
                <strong>{review.name}</strong>
              </div>
            </div>
          ))}
        </div>

        <div className="reviews-cta">
          <p>Ready to make Crimson Crossing your home?</p>
          <a href="#contact" className="btn">Contact Us Today</a>
        </div>
      </div>
    </section>
  )
}

export default Reviews
