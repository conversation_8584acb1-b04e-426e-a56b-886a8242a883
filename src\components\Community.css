.community {
  background: var(--global-palette9);
}

.community-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.community-text h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  line-height: 1.3;
}

.community-text p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.7;
  color: var(--global-palette5);
}

.community-image {
  position: relative;
}

.community-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  border-radius: 12px;
  box-shadow: 0px 15px 35px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .community-content {
    gap: 3rem;
  }
  
  .community-text h2 {
    font-size: 2rem;
  }
  
  .community-image img {
    height: 350px;
  }
}

@media (max-width: 767px) {
  .community-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    text-align: center;
  }
  
  .community-text h2 {
    font-size: 1.8rem;
  }
  
  .community-text p {
    font-size: 1rem;
  }
  
  .community-image img {
    height: 300px;
  }
}
