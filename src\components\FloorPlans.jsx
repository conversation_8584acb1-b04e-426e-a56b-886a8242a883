import React from 'react'
import './FloorPlans.css'

const FloorPlans = () => {
  const floorPlans = [
    {
      name: 'The Banner (remodeled)',
      bedrooms: 3,
      bathrooms: 3.5,
      features: ['Den', 'Remodeled', 'Individual Bathrooms'],
      image: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: '3 Bed/3.5 Bath with Den - Newly remodeled with modern finishes'
    },
    {
      name: 'The Banner',
      bedrooms: 3,
      bathrooms: 3.5,
      features: ['Den', 'Individual Bathrooms', 'Spacious Layout'],
      image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: '3 Bed/3.5 Bath with Den - Classic layout with plenty of space'
    },
    {
      name: 'The Crimson',
      bedrooms: 3,
      bathrooms: 3.5,
      features: ['Individual Bathrooms', 'Open Concept', 'Modern Kitchen'],
      image: 'https://images.unsplash.com/photo-1484154218962-a197022b5858?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: '3 Bed/3.5 Bath - Open concept living with modern amenities'
    },
    {
      name: 'The Knight',
      bedrooms: 3,
      bathrooms: 2.5,
      features: ['Efficient Layout', 'Shared Bathrooms', 'Affordable'],
      image: 'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
      description: '3 Bed/2.5 Bath - Efficient design perfect for students'
    }
  ]

  return (
    <section className="floor-plans section-large" id="floorplans">
      <div className="container">
        <div className="section-header">
          <h2>4 Unique Apartment Floor Plans</h2>
          <p>
            Our apartment floor plans come in either 3 or 4 bedroom options and have either 
            2.5 or 3.5 bathrooms. Individual outside entrances make it easy to access your 
            apartment and enjoy the privacy of no shared interior hallways.
          </p>
        </div>

        <div className="floor-plans-features">
          <div className="features-grid">
            <div className="feature-item">
              <span className="feature-icon">🏠</span>
              <span>Choose from 3-4 bedrooms</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🚿</span>
              <span>2.5 - 3.5 bathrooms</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🚪</span>
              <span>Individual outside entrances</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🧺</span>
              <span>In-Unit Laundry</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🍳</span>
              <span>Modern Kitchen Appliances</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🛁</span>
              <span>Individual Bathrooms (or Shared by Only 2)</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">💨</span>
              <span>Ceiling Fans in Bedrooms</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🐕</span>
              <span>Pet-Friendly Options (for Emotional Support Animals)</span>
            </div>
          </div>
        </div>

        <div className="floor-plans-grid">
          {floorPlans.map((plan, index) => (
            <div key={index} className="floor-plan-card">
              <div className="plan-image">
                <img src={plan.image} alt={plan.name} />
                <div className="plan-overlay">
                  <button className="view-plan-btn">View Floor Plan</button>
                </div>
              </div>
              <div className="plan-content">
                <h3>{plan.name}</h3>
                <div className="plan-specs">
                  <span className="spec">{plan.bedrooms} Bed</span>
                  <span className="spec">{plan.bathrooms} Bath</span>
                </div>
                <p>{plan.description}</p>
                <div className="plan-features">
                  {plan.features.map((feature, idx) => (
                    <span key={idx} className="feature-tag">{feature}</span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="floor-plans-cta">
          <a href="#contact" className="btn">View Our Floorplans</a>
        </div>
      </div>
    </section>
  )
}

export default FloorPlans
