.floor-plans {
  background: var(--global-palette9);
  width: 100%;
  overflow-x: hidden;
}

.floor-plans-features {
  margin-bottom: 4rem;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  background: var(--global-palette8);
  padding: 2rem;
  border-radius: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.95rem;
  color: var(--global-palette4);
}

.feature-icon {
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.floor-plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.floor-plan-card {
  background: var(--global-palette9);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0px 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.floor-plan-card:hover {
  transform: translateY(-5px);
  box-shadow: 0px 15px 35px rgba(0,0,0,0.15);
}

.plan-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.plan-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.floor-plan-card:hover .plan-image img {
  transform: scale(1.05);
}

.plan-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(153, 29, 32, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.floor-plan-card:hover .plan-overlay {
  opacity: 1;
}

.view-plan-btn {
  background: var(--global-palette9);
  color: var(--global-palette1);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-plan-btn:hover {
  background: var(--global-palette7);
}

.plan-content {
  padding: 1.5rem;
}

.plan-content h3 {
  font-size: 1.4rem;
  margin-bottom: 0.5rem;
  color: var(--global-palette3);
}

.plan-specs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.spec {
  background: var(--global-palette1);
  color: var(--global-palette9);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
}

.plan-content p {
  color: var(--global-palette5);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.plan-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  background: var(--global-palette8);
  color: var(--global-palette4);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.floor-plans-cta {
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .floor-plans-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 767px) {
  .features-grid {
    grid-template-columns: 1fr;
    padding: 1.5rem;
  }
  
  .floor-plans-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .plan-image {
    height: 200px;
  }
  
  .plan-content {
    padding: 1rem;
  }
  
  .plan-specs {
    justify-content: center;
  }
}
