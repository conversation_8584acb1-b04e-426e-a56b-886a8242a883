.amenities {
  background: var(--global-palette8);
  width: 100%;
  overflow-x: hidden;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--global-palette5);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.amenities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.amenity-card {
  background: var(--global-palette9);
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0px 5px 15px rgba(0,0,0,0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.amenity-card:hover {
  transform: translateY(-5px);
  box-shadow: 0px 15px 35px rgba(0,0,0,0.15);
}

.amenity-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--global-palette1), var(--global-palette2));
  border-radius: 50%;
  margin: 0 auto 1.5rem;
  color: var(--global-palette9);
}

.amenity-card h3 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: var(--global-palette3);
}

.amenity-card p {
  color: var(--global-palette5);
  line-height: 1.6;
}

.amenities-cta {
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .amenities-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .section-header h2 {
    font-size: 2rem;
  }
}

@media (max-width: 767px) {
  .amenities-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .amenity-card {
    padding: 1.5rem;
  }
  
  .amenity-icon {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  .section-header h2 {
    font-size: 1.8rem;
  }
  
  .section-header p {
    font-size: 1rem;
  }
}
