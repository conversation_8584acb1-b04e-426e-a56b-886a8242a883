/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&family=Roboto+Slab:wght@700&display=swap');

/* CSS Variables - Color Palette from original site */
:root {
  --global-palette1: #991d20;
  --global-palette2: #bc2426;
  --global-palette3: #414042;
  --global-palette4: #525053;
  --global-palette5: #5c5a5e;
  --global-palette6: #6d6e71;
  --global-palette7: #eaeaeb;
  --global-palette8: #f1f1f2;
  --global-palette9: #ffffff;

  --global-body-font-family: 'Poppins', sans-serif;
  --global-heading-font-family: 'Roboto Slab', serif;
  --global-content-width: 1290px;
  --global-content-edge-padding: 1.5rem;
}

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--global-body-font-family);
  font-size: 18px;
  line-height: 1.6;
  color: var(--global-palette4);
  background: var(--global-palette8);
}

.App {
  min-height: 100vh;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--global-heading-font-family);
  font-weight: 700;
  color: var(--global-palette3);
}

h1 {
  font-size: 32px;
  line-height: 1.5;
}

h2 {
  font-size: 28px;
  line-height: 1.5;
}

h3 {
  font-size: 24px;
  line-height: 1.5;
  color: var(--global-palette6);
}

h4 {
  font-size: 22px;
  line-height: 1.5;
}

h5 {
  font-size: 20px;
  line-height: 1.5;
}

h6 {
  font-size: 18px;
  line-height: 1.5;
  color: var(--global-palette5);
}

/* Container styles */
.container {
  max-width: var(--global-content-width);
  margin: 0 auto;
  padding: 0 var(--global-content-edge-padding);
}

/* Button styles */
.btn {
  display: inline-block;
  padding: 0.4em 1em;
  background-color: var(--global-palette1);
  color: var(--global-palette9);
  text-decoration: none;
  border-radius: 8px;
  border: none;
  font-family: inherit;
  font-size: inherit;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0px 0px 0px -7px rgba(0,0,0,0);
}

.btn:hover {
  background-color: var(--global-palette2);
  box-shadow: 0px 15px 25px -7px rgba(0,0,0,0.1);
}

.btn-outline {
  background-color: transparent;
  color: var(--global-palette1);
  border: 2px solid var(--global-palette1);
}

.btn-outline:hover {
  background-color: var(--global-palette1);
  color: var(--global-palette9);
}

/* Section spacing */
.section {
  padding: 3rem 0;
}

.section-large {
  padding: 5rem 0;
}

/* Responsive design */
@media (max-width: 1024px) {
  :root {
    --global-content-edge-padding: 2rem;
  }

  .section {
    padding: 2rem 0;
  }

  .section-large {
    padding: 3rem 0;
  }
}

@media (max-width: 767px) {
  :root {
    --global-content-edge-padding: 1.5rem;
  }

  body {
    font-size: 16px;
  }

  h1 {
    font-size: 28px;
  }

  h2 {
    font-size: 24px;
  }

  h3 {
    font-size: 20px;
  }

  .section {
    padding: 1.5rem 0;
  }

  .section-large {
    padding: 2rem 0;
  }
}
