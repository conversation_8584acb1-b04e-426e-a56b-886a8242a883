import React from 'react'
import './Amenities.css'

const Amenities = () => {
  const amenities = [
    {
      icon: '🏠',
      title: 'Individual Outside Entrances',
      description: 'Private entrances for each apartment unit'
    },
    {
      icon: '🧺',
      title: 'In-Unit Laundry',
      description: 'Washer and dryer in every apartment'
    },
    {
      icon: '🍳',
      title: 'Modern Kitchen Appliances',
      description: 'Updated appliances in all units'
    },
    {
      icon: '🚿',
      title: 'Individual Bathrooms',
      description: 'Private bathrooms or shared by only 2'
    },
    {
      icon: '💨',
      title: 'Ceiling Fans in Bedrooms',
      description: 'Climate control in every bedroom'
    },
    {
      icon: '🐕',
      title: 'Pet-Friendly Options',
      description: 'Emotional Support Animals welcome'
    },
    {
      icon: '🚗',
      title: 'Parking Available',
      description: 'Convenient parking for residents'
    },
    {
      icon: '🏃‍♂️',
      title: 'Fitness Center Access',
      description: 'Stay active with nearby fitness facilities'
    }
  ]

  return (
    <section className="amenities section-large" id="amenities">
      <div className="container">
        <div className="section-header">
          <h2>Amenities</h2>
          <p>
            Explore our in-unit, building, and community amenities, along with convenient 
            services that simplify life and provide peace of mind at Crimson Crossing.
          </p>
        </div>
        
        <div className="amenities-grid">
          {amenities.map((amenity, index) => (
            <div key={index} className="amenity-card">
              <div className="amenity-icon">
                {amenity.icon}
              </div>
              <h3>{amenity.title}</h3>
              <p>{amenity.description}</p>
            </div>
          ))}
        </div>
        
        <div className="amenities-cta">
          <a href="#contact" className="btn">View All Amenities</a>
        </div>
      </div>
    </section>
  )
}

export default Amenities
